/* Categories Component Styles */

.categories-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px;
  background-color: #ffffff;
}

/* Header Styles */
.categories-header {
  text-align: center;
  margin-bottom: 50px;
}

.categories-title {
  font-size: 2.5rem;
  font-weight: 400;
  color: #333333;
  margin-bottom: 8px;
  font-family: 'Georgia', serif;
}

.categories-subtitle {
  font-size: 1rem;
  color: #888888;
  margin-bottom: 0;
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* Desktop Grid Layout */
.desktop-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

/* Mobile Categories Container */
.mobile-categories-container {
  display: none;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.mobile-categories-slider {
  display: flex;
  transition: transform 0.3s ease;
  width: 200%;
  height: auto;
  will-change: transform;
}

.mobile-slide {
  width: 50%;
  flex-shrink: 0;
  padding: 0 15px;
  box-sizing: border-box;
  min-height: 300px;
}

.mobile-categories-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 15px;
  width: 100%;
  height: 100%;
}

/* Slide Indicators */
.slide-indicators {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 30px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background-color: #b7b7b7;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.indicator.active {
  background-color: #000000;
}

.indicator:hover {
  background-color: #b8941f;
}

/* Category Items */
.category-item {
  position: relative;
  transition: all 0.3s ease;
}

.category-item:hover {
  transform: translateY(-2px);
}

.category-link {
  display: block;
  text-decoration: none;
  color: inherit;
  height: 100%;
}

.category-card {
  background: #ffffff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.category-item:hover .category-card {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.category-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-item:hover .category-image {
  transform: scale(1.02);
}

.category-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: #333333;
  margin: 0;
  padding: 15px 20px;
  text-align: center;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  background: #a43232;
}

/* View All Section */
.category-view-all {
  color: #333333;
}

.view-all-link {
  color: inherit;
  height: 100%;
}

.view-all-card {
  background: #f8f9fa;
  border-radius: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.category-view-all:hover .view-all-card {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.view-all-content {
  margin-bottom: 20px;
}

.view-all-number {
  font-size: 3rem;
  font-weight: 300;
  margin: 0 0 5px 0;
  color: #333333;
  font-family: 'Georgia', serif;
}

.view-all-text {
  font-size: 0.8rem;
  margin: 0;
  color: #666666;
  line-height: 1.4;
}

.view-all-title {
  font-size: 0.9rem;
  font-weight: 500;
  margin: 0;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  color: #333333;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .desktop-grid {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 15px;
  }

  .categories-title {
    font-size: 2.2rem;
  }

  .category-image {
    height: 160px;
  }
}

@media (max-width: 768px) {
  .categories-section {
    padding: 40px 15px;
  }

  /* Hide desktop grid, show mobile container */
  .desktop-grid {
    display: none;
  }

  .mobile-categories-container {
    display: block;
  }

  .categories-title {
    font-size: 2rem;
  }

  .categories-subtitle {
    font-size: 0.9rem;
  }

  .category-image {
    height: 140px;
  }

  .category-title {
    font-size: 0.8rem;
    padding: 12px 15px;
  }

  .view-all-number {
    font-size: 2.5rem;
  }

  .view-all-text {
    font-size: 0.7rem;
  }

  .view-all-title {
    font-size: 0.8rem;
  }

  .view-all-card {
    padding: 25px 15px;
  }
}

@media (max-width: 480px) {
  .categories-section {
    padding: 30px 10px;
  }

  .categories-title {
    font-size: 1.8rem;
  }

  .mobile-categories-grid {
    gap: 10px;
  }

  .category-image {
    height: 120px;
  }

  .category-title {
    font-size: 0.7rem;
    padding: 10px 12px;
  }

  .view-all-number {
    font-size: 2rem;
  }

  .view-all-text {
    font-size: 0.6rem;
  }

  .view-all-title {
    font-size: 0.7rem;
  }

  .view-all-card {
    padding: 20px 10px;
  }
}


