/* Categories Component Styles */

.categories-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 80px 40px;
  background-color: #ffffff;
}

/* Header Styles */
.categories-header {
  text-align: center;
  margin-bottom: 60px;
}

.categories-title {
  font-size: 2.8rem;
  font-weight: 400;
  color: #2c2c2c;
  margin-bottom: 12px;
  font-family: 'Georgia', serif;
  line-height: 1.2;
}

.categories-subtitle {
  font-size: 1.1rem;
  color: #8a8a8a;
  margin-bottom: 0;
  font-weight: 400;
  letter-spacing: 0.3px;
}

/* Desktop Grid Layout */
.desktop-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 24px;
  max-width: 1000px;
  margin: 0 auto;
}

/* Mobile Categories Container */
.mobile-categories-container {
  display: none;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.mobile-categories-slider {
  display: flex;
  transition: transform 0.3s ease;
  width: 200%;
  height: auto;
  will-change: transform;
}

.mobile-slide {
  width: 50%;
  flex-shrink: 0;
  padding: 0 15px;
  box-sizing: border-box;
  min-height: 300px;
}

.mobile-categories-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 20px;
  width: 100%;
  height: 100%;
}

/* Slide Indicators */
.slide-indicators {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 30px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background-color: #b7b7b7;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.indicator.active {
  background-color: #000000;
}

.indicator:hover {
  background-color: #b8941f;
}

/* Category Items */
.category-item {
  position: relative;
  transition: all 0.3s ease;
}

.category-item:hover {
  transform: translateY(-2px);
}

.category-link {
  display: block;
  text-decoration: none;
  color: inherit;
  height: 100%;
}

.category-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  height: 280px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.category-item:hover .category-card {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.category-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-item:hover .category-image {
  transform: scale(1.02);
}

.category-title {
  font-size: 0.85rem;
  font-weight: 600;
  color: #2c2c2c;
  margin: 0;
  padding: 18px 20px;
  text-align: center;
  letter-spacing: 1px;
  text-transform: uppercase;
  background: #ffffff;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* View All Section */
.category-view-all {
  color: #333333;
}

.view-all-link {
  color: inherit;
  height: 100%;
}

.view-all-card {
  background: #f8f8f8;
  border-radius: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.category-view-all:hover .view-all-card {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.view-all-content {
  margin-bottom: 24px;
}

.view-all-number {
  font-size: 3.5rem;
  font-weight: 400;
  margin: 0 0 8px 0;
  color: #2c2c2c;
  font-family: 'Georgia', serif;
  line-height: 1;
}

.view-all-text {
  font-size: 0.9rem;
  margin: 0;
  color: #666666;
  line-height: 1.3;
  font-weight: 400;
}

.view-all-title {
  font-size: 0.85rem;
  font-weight: 600;
  margin: 0;
  letter-spacing: 1px;
  text-transform: uppercase;
  color: #2c2c2c;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .desktop-grid {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 20px;
  }

  .categories-title {
    font-size: 2.4rem;
  }

  .category-image {
    height: 200px;
  }

  .categories-section {
    padding: 60px 30px;
  }
}

@media (max-width: 768px) {
  .categories-section {
    padding: 50px 20px;
  }

  /* Hide desktop grid, show mobile container */
  .desktop-grid {
    display: none;
  }

  .mobile-categories-container {
    display: block;
  }

  .categories-title {
    font-size: 2.2rem;
  }

  .categories-subtitle {
    font-size: 1rem;
  }

  .category-image {
    height: 180px;
  }

  .category-title {
    font-size: 0.75rem;
    padding: 14px 16px;
    letter-spacing: 0.8px;
  }

  .view-all-number {
    font-size: 3rem;
  }

  .view-all-text {
    font-size: 0.8rem;
  }

  .view-all-title {
    font-size: 0.75rem;
  }

  .view-all-card {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .categories-section {
    padding: 40px 16px;
  }

  .categories-title {
    font-size: 2rem;
  }

  .categories-subtitle {
    font-size: 0.9rem;
  }

  .mobile-categories-grid {
    gap: 16px;
  }

  .category-image {
    height: 160px;
  }

  .category-title {
    font-size: 0.7rem;
    padding: 12px 14px;
    letter-spacing: 0.6px;
  }

  .view-all-number {
    font-size: 2.8rem;
  }

  .view-all-text {
    font-size: 0.75rem;
  }

  .view-all-title {
    font-size: 0.7rem;
  }

  .view-all-card {
    padding: 25px 16px;
  }
}

/* Category-specific background colors to match Tanishq design */
.category-earrings .category-card {
  background: linear-gradient(135deg, #f5f1e8 0%, #e8dcc0 100%);
}

.category-rings .category-card {
  background: linear-gradient(135deg, #f4e6d7 0%, #e6d4b7 100%);
}

.category-pendants .category-card {
  background: linear-gradient(135deg, #e8e3f0 0%, #d4c5e8 100%);
}

.category-mangalsutra .category-card {
  background: linear-gradient(135deg, #d8e8e0 0%, #b8d4c0 100%);
}

.category-bracelets .category-card {
  background: linear-gradient(135deg, #e0f0f8 0%, #c0e0f0 100%);
}

.category-bangles .category-card {
  background: linear-gradient(135deg, #f5f1e8 0%, #e8dcc0 100%);
}

.category-chains .category-card {
  background: linear-gradient(135deg, #f4e6d7 0%, #e6d4b7 100%);
}


