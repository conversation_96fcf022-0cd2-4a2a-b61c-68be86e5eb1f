'use client';

import React, { useState, useRef, useEffect } from 'react';
import '../style/categories.css';

interface CategoryItem {
  id: number;
  src: string;
  alt: string;
  href: string;
  title: string;
  className: string;
}

const Categories: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const touchStartX = useRef(0);
  const touchEndX = useRef(0);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const categoryItems: CategoryItem[] = [
    {
      id: 1,
      src: "/images/image1.png",
      alt: "Earrings",
      href: "#earrings",
      title: "EARRINGS",
      className: "category-earrings"
    },
    {
      id: 2,
      src: "/images/image2.png",
      alt: "Finger Rings",
      href: "#finger-rings",
      title: "FINGER RINGS",
      className: "category-rings"
    },
    {
      id: 3,
      src: "/images/image3.png",
      alt: "Pendants",
      href: "#pendants",
      title: "PENDANTS",
      className: "category-pendants"
    },
    {
      id: 4,
      src: "/images/image4.png",
      alt: "Mangalsutra",
      href: "#mangalsutra",
      title: "MANGALSUTRA",
      className: "category-mangalsutra"
    },
    {
      id: 5,
      src: "/images/image5.png",
      alt: "Bracelets",
      href: "#bracelets",
      title: "BRACELETS",
      className: "category-bracelets"
    },
    {
      id: 6,
      src: "/images/image1.png",
      alt: "Bangles",
      href: "#bangles",
      title: "BANGLES",
      className: "category-bangles"
    },
    {
      id: 7,
      src: "/images/image2.png",
      alt: "Chains",
      href: "#chains",
      title: "CHAINS",
      className: "category-chains"
    }
  ];

  const handleCategoryClick = (href: string) => {
    console.log(`Navigating to: ${href}`);
    // Add your navigation logic here
  };

  // Touch handlers for swipe functionality
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.targetTouches[0].clientX;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndX.current = e.targetTouches[0].clientX;
  };

  const handleTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return;

    const distance = touchStartX.current - touchEndX.current;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && currentSlide < 1) {
      setCurrentSlide(1);
    }
    if (isRightSwipe && currentSlide > 0) {
      setCurrentSlide(0);
    }
  };

  // Split categories into groups of 4 for mobile
  const getCategoriesForSlide = (slideIndex: number) => {
    const startIndex = slideIndex * 4;
    const endIndex = startIndex + 4;
    return categoryItems.slice(startIndex, endIndex);
  };

  // Get view all item for second slide
  const getViewAllForSlide = (slideIndex: number) => {
    if (slideIndex === 1) {
      return (
        <div className="category-item category-view-all">
          <a
            href="#view-all"
            onClick={(e) => {
              e.preventDefault();
              handleCategoryClick("#view-all");
            }}
            className="category-link view-all-link"
          >
            <div className="view-all-content">
              <h3 className="view-all-number">10+</h3>
              <p className="view-all-text">Categories to choose from</p>
              <h4 className="view-all-title">VIEW ALL</h4>
            </div>
          </a>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="categories-section">
      <div className="categories-header">
        <h1 className="categories-title">Find Your Perfect Match</h1>
        <h2 className="categories-subtitle">Shop by Categories</h2>
      </div>

      {/* Desktop/Tablet Grid Layout */}
      <div className="categories-grid desktop-grid">
        {categoryItems.map((item) => (
          <div key={item.id} className={`category-item ${item.className}`}>
            <a
              href={item.href}
              onClick={(e) => {
                e.preventDefault();
                handleCategoryClick(item.href);
              }}
              className="category-link"
            >
              <div className="category-image-container">
                <img
                  src={item.src}
                  alt={item.alt}
                  className="category-image"
                />
              </div>
              <h3 className="category-title">{item.title}</h3>
            </a>
          </div>
        ))}

        {/* View All Section */}
        <div className="category-item category-view-all">
          <a
            href="#view-all"
            onClick={(e) => {
              e.preventDefault();
              handleCategoryClick("#view-all");
            }}
            className="category-link view-all-link"
          >
            <div className="view-all-content">
              <h3 className="view-all-number">10+</h3>
              <p className="view-all-text">Categories to choose from</p>
              <h4 className="view-all-title">VIEW ALL</h4>
            </div>
          </a>
        </div>
      </div>

      {/* Mobile Swipe Layout */}
      <div className="mobile-categories-container">
        <div
          className="mobile-categories-slider"
          style={{ transform: `translateX(-${currentSlide * 50}%)` }}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {/* First slide - Categories 1-4 */}
          <div className="mobile-slide">
            <div className="mobile-categories-grid">
              {getCategoriesForSlide(0).map((item) => (
                <div key={item.id} className={`category-item ${item.className}`}>
                  <a
                    href={item.href}
                    onClick={(e) => {
                      e.preventDefault();
                      handleCategoryClick(item.href);
                    }}
                    className="category-link"
                  >
                    <div className="category-image-container">
                      <img
                        src={item.src}
                        alt={item.alt}
                        className="category-image"
                      />
                    </div>
                    <h3 className="category-title">{item.title}</h3>
                  </a>
                </div>
              ))}
            </div>
          </div>

          {/* Second slide - Categories 5-7 + View All */}
          <div className="mobile-slide">
            <div className="mobile-categories-grid">
              {getCategoriesForSlide(1).map((item) => (
                <div key={item.id} className={`category-item ${item.className}`}>
                  <a
                    href={item.href}
                    onClick={(e) => {
                      e.preventDefault();
                      handleCategoryClick(item.href);
                    }}
                    className="category-link"
                  >
                    <div className="category-image-container">
                      <img
                        src={item.src}
                        alt={item.alt}
                        className="category-image"
                      />
                    </div>
                    <h3 className="category-title">{item.title}</h3>
                  </a>
                </div>
              ))}
              {getViewAllForSlide(1)}
            </div>
          </div>
        </div>

        {/* Slide indicators */}
        <div className="slide-indicators">
          <button
            className={`indicator ${currentSlide === 0 ? 'active' : ''}`}
            onClick={() => setCurrentSlide(0)}
            aria-label="Go to slide 1"
          />
          <button
            className={`indicator ${currentSlide === 1 ? 'active' : ''}`}
            onClick={() => setCurrentSlide(1)}
            aria-label="Go to slide 2"
          />
        </div>
      </div>
    </div>
  );
};

export default Categories;
