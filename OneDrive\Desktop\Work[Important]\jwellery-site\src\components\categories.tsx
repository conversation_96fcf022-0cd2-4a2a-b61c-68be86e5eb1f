'use client';

import React from 'react';
import '../style/categories.css';

interface CategoryItem {
  id: number;
  src: string;
  alt: string;
  href: string;
  title: string;
  className: string;
}

const Categories: React.FC = () => {
  const categoryItems: CategoryItem[] = [
    {
      id: 1,
      src: "/images/image1.png",
      alt: "Earrings",
      href: "#earrings",
      title: "EARRINGS",
      className: "category-earrings"
    },
    {
      id: 2,
      src: "/images/image2.png",
      alt: "Finger Rings",
      href: "#finger-rings",
      title: "FINGER RINGS",
      className: "category-rings"
    },
    {
      id: 3,
      src: "/images/image3.png",
      alt: "Pendants",
      href: "#pendants",
      title: "PENDANTS",
      className: "category-pendants"
    },
    {
      id: 4,
      src: "/images/image4.png",
      alt: "Mangalsutra",
      href: "#mangalsutra",
      title: "MANGALSUTRA",
      className: "category-mangalsutra"
    },
    {
      id: 5,
      src: "/images/image5.png",
      alt: "Bracelets",
      href: "#bracelets",
      title: "BRACELETS",
      className: "category-bracelets"
    },
    {
      id: 6,
      src: "/images/image1.png",
      alt: "Bangles",
      href: "#bangles",
      title: "BANGLES",
      className: "category-bangles"
    },
    {
      id: 7,
      src: "/images/image2.png",
      alt: "Chains",
      href: "#chains",
      title: "CHAINS",
      className: "category-chains"
    }
  ];

  const handleCategoryClick = (href: string) => {
    console.log(`Navigating to: ${href}`);
    // Add your navigation logic here
  };

  return (
    <div className="categories-section">
      <div className="categories-header">
        <h1 className="categories-title">Find Your Perfect Match</h1>
        <h2 className="categories-subtitle">Shop by Categories</h2>
      </div>

      <div className="categories-grid">
        {categoryItems.map((item) => (
          <div key={item.id} className={`category-item ${item.className}`}>
            <a
              href={item.href}
              onClick={(e) => {
                e.preventDefault();
                handleCategoryClick(item.href);
              }}
              className="category-link"
            >
              <div className="category-image-container">
                <img
                  src={item.src}
                  alt={item.alt}
                  className="category-image"
                />
              </div>
              <h3 className="category-title">{item.title}</h3>
            </a>
          </div>
        ))}

        {/* View All Section */}
        <div className="category-item category-view-all">
          <a
            href="#view-all"
            onClick={(e) => {
              e.preventDefault();
              handleCategoryClick("#view-all");
            }}
            className="category-link view-all-link"
          >
            <div className="view-all-content">
              <h3 className="view-all-number">10+</h3>
              <p className="view-all-text">Categories to choose from</p>
              <h4 className="view-all-title">VIEW ALL</h4>
            </div>
          </a>
        </div>
      </div>
    </div>
  );
};

export default Categories;
